import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/host/presentation/views/host_reviews_page.dart';
import 'package:gather_point/feature/profile/presentation/views/my_bookings_screen.dart';
import 'package:gather_point/feature/host/presentation/cubit/host_dashboard_cubit.dart';
import 'package:gather_point/feature/host/data/models/host_dashboard_model.dart';
import 'package:gather_point/feature/host/presentation/views/create_property_page.dart';
import 'package:gather_point/feature/host/presentation/views/host_reservations_page.dart';
import 'package:gather_point/feature/host/presentation/views/withdrawal_management_page.dart';
import 'package:gather_point/core/services/service_locator.dart';

class HostHomePage extends StatelessWidget {
  const HostHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HostDashboardCubit(getIt())..loadDashboardData(),
      child: const _HostHomePageContent(),
    );
  }
}

class _HostHomePageContent extends StatefulWidget {
  const _HostHomePageContent();

  @override
  State<_HostHomePageContent> createState() => _HostHomePageContentState();
}

class _HostHomePageContentState extends State<_HostHomePageContent> {
  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedPageLayout(
      hasBottomNavigation: true,
      title: 'لوحة المضيف',
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreatePropertyPage(),
            ),
          );

          if (result == true && mounted) {
            // Refresh dashboard data after successful property creation
            context.read<HostDashboardCubit>().refreshDashboard();
          }
        },
        icon: const Icon(Icons.add_home_rounded),
        label: const Text('إضافة عقار'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<HostDashboardCubit, HostDashboardState>(
        builder: (context, state) {
          if (state is HostDashboardLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          if (state is HostDashboardError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.withValues(alpha: 0.6),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'خطأ في تحميل البيانات',
                    style: AppTextStyles.font16SemiBold,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  EnhancedButton(
                    text: 'إعادة المحاولة',
                    onPressed: () {
                      context.read<HostDashboardCubit>().loadDashboardData();
                    },
                    icon: Icons.refresh,
                  ),
                ],
              ),
            );
          }
          
          // Use loaded data or fallback to dummy data
          HostDashboardModel? dashboardData;
          if (state is HostDashboardLoaded) {
            dashboardData = state.dashboardData;
          }
          
          return RefreshIndicator(
            onRefresh: () async {
              context.read<HostDashboardCubit>().refreshDashboard();
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Wallet & Earnings Overview
                  _buildFinancialOverview(context, s, dashboardData?.financialData),
                  
                  const SizedBox(height: 24),
                  
                  // Charts Section
                  _buildChartsSection(context, s, dashboardData),
                  
                  const SizedBox(height: 24),
                  
                  // Recent Bookings
                  _buildRecentBookings(context, s, dashboardData?.recentReservations),
                  
                  const SizedBox(height: 24),
                  
                  // Recent Reviews
                  _buildRecentReviews(context, s, dashboardData?.recentReviews),
                  
                  const SizedBox(height: 100), // Bottom padding for navigation
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFinancialOverview(BuildContext context, S s, [HostFinancialData? financialData]) {
    // Use API data if available, otherwise use dummy data
    final walletBalance = financialData?.walletBalance ?? 2450.75;
    final totalEarnings = financialData?.totalEarnings ?? 15680.50;
    final pendingEarnings = financialData?.pendingEarnings ?? 890.25;
    final totalWithdrawn = financialData?.totalWithdrawn ?? 12340.00;
    final thisMonthEarnings = financialData?.thisMonthEarnings ?? 3650.75;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with wallet balance
        EnhancedCard(
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  AppColors.yellow.withValues(alpha: 0.1),
                  AppColors.yellow.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.walletBalance,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '\$${walletBalance.toStringAsFixed(2)}',
                        style: AppTextStyles.font24Bold.copyWith(
                          color: AppColors.yellow,
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WithdrawalManagementPage(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.download_rounded),
                  label: Text(s.withdraw),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.yellow.withValues(alpha: 0.1),
                    foregroundColor: AppColors.yellow,
                    elevation: 0,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Stats Grid - One per row for better visibility
        HostStatCard(
          title: s.totalEarnings,
          value: '\$${totalEarnings.toStringAsFixed(2)}',
          icon: Icons.trending_up_rounded,
          color: Colors.green,
        ),

        const SizedBox(height: 12),

        HostStatCard(
          title: s.pendingEarnings,
          value: '\$${pendingEarnings.toStringAsFixed(2)}',
          icon: Icons.schedule_rounded,
          color: Colors.orange,
        ),

        const SizedBox(height: 12),

        HostStatCard(
          title: s.totalWithdrawn,
          value: '\$${totalWithdrawn.toStringAsFixed(2)}',
          icon: Icons.download_rounded,
          color: Colors.blue,
        ),

        const SizedBox(height: 12),

        HostStatCard(
          title: s.thisMonth,
          value: '\$${thisMonthEarnings.toStringAsFixed(2)}',
          icon: Icons.calendar_month_rounded,
          color: AppColors.yellow,
        ),
      ],
    );
  }

  Widget _buildChartsSection(BuildContext context, S s, [HostDashboardModel? dashboardData]) {
    return Column(
      children: [
        _buildEarningsChart(context, s, dashboardData),
        const SizedBox(height: 16),
        _buildBookingsChart(context, s, dashboardData),
      ],
    );
  }

  Widget _buildEarningsChart(BuildContext context, S s, [HostDashboardModel? dashboardData]) {
    // Use API data if available, otherwise use dummy data
    final List<double> earningsData = dashboardData?.earningsChart.map((e) => e.amount).toList() ?? 
        [1200, 1800, 1500, 2200, 1900, 2450];
        
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  s.earningsChart,
                  style: AppTextStyles.font18Bold.copyWith(
                    color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.yellow.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.yellow.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    s.last6Months,
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: AppColors.yellow,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 180,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: earningsData.isNotEmpty ? earningsData.asMap().entries.map((entry) {
                  final index = entry.key;
                  final value = entry.value;
                  final maxValue = earningsData.reduce((a, b) => a > b ? a : b);
                  // Reduced max height to account for text labels and spacing
                  final height = maxValue > 0 ? (value / maxValue) * 80.0 : 20.0;

                  return Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Value label
                        Text(
                          '\$${value.toStringAsFixed(0)}',
                          style: AppTextStyles.font10Regular.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 4),
                        // Bar - using Flexible to prevent overflow
                        Flexible(
                          child: Container(
                            width: 24,
                            height: height,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.yellow,
                                  AppColors.yellow.withValues(alpha: 0.7),
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.yellow.withValues(alpha: 0.3),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Month label
                        Text(
                          '${index + 1}',
                          style: AppTextStyles.font10Regular.copyWith(
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList() : [
                  const Center(
                    child: Text('لا توجد بيانات'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingsChart(BuildContext context, S s, [HostDashboardModel? dashboardData]) {
    // Use API data if available, otherwise use dummy data
    final List<double> bookingsData = dashboardData?.bookingsChart.map((e) => e.count.toDouble()).toList() ??
        [8, 12, 10, 15, 13, 18];

    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  s.bookingsChart,
                  style: AppTextStyles.font18Bold.copyWith(
                    color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    s.last6Months,
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: Colors.blue[800],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 180,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: bookingsData.isNotEmpty ? bookingsData.asMap().entries.map((entry) {
                  final index = entry.key;
                  final value = entry.value;
                  final maxValue = bookingsData.reduce((a, b) => a > b ? a : b);
                  // Reduced max height to account for text labels and spacing
                  final height = maxValue > 0 ? (value / maxValue) * 80.0 : 20.0;

                  return Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Value label
                        Text(
                          value.toStringAsFixed(0),
                          style: AppTextStyles.font10Regular.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 4),
                        // Bar - using Flexible to prevent overflow
                        Flexible(
                          child: Container(
                            width: 24,
                            height: height,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.blue,
                                  Colors.blue.withValues(alpha: 0.7),
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.blue.withValues(alpha: 0.3),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Month label
                        Text(
                          '${index + 1}',
                          style: AppTextStyles.font10Regular.copyWith(
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList() : [
                  const Center(
                    child: Text('لا توجد بيانات'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentBookings(BuildContext context, S s, [List<ReservationSummary>? apiReservations]) {
    // Use API data if available, otherwise use dummy data
    final recentBookings = apiReservations ?? _getDummyBookings();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              s.recentBookings,
              style: AppTextStyles.font20Bold.copyWith(
                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
              ),
            ),
            TextButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const HostReservationsPage(),
                  ),
                );
              },
              icon: const Icon(Icons.arrow_forward_ios_rounded, size: 16),
              label: Text(s.viewAll),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (recentBookings.isEmpty)
          EnhancedCard(
            child: Padding(
              padding: const EdgeInsets.all(40),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.calendar_today_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      s.noBookingsYet,
                      style: AppTextStyles.font16SemiBold.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          ...recentBookings.take(3).map((booking) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: HostBookingCard(booking: booking, s: s),
          )),
      ],
    );
  }

  Widget _buildRecentReviews(BuildContext context, S s, [List<ReviewSummary>? apiReviews]) {
    // Use API data if available, otherwise use dummy data
    final recentReviews = apiReviews ?? _getDummyReviews();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              s.recentReviews,
              style: AppTextStyles.font20Bold.copyWith(
                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
              ),
            ),
            TextButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const HostReviewsPage(),
                  ),
                );
              },
              icon: const Icon(Icons.arrow_forward_ios_rounded, size: 16),
              label: Text(s.viewAll),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (recentReviews.isEmpty)
          EnhancedCard(
            child: Padding(
              padding: const EdgeInsets.all(40),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.star_outline_rounded,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد تقييمات بعد',
                      style: AppTextStyles.font16SemiBold.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          ...recentReviews.take(3).map((review) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: HostReviewCard(review: review, s: s),
          )),
      ],
    );
  }

  void _showWithdrawDialog(BuildContext context, S s) {
    final TextEditingController amountController = TextEditingController();
    String selectedMethod = 'bankTransfer';

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return BlocBuilder<HostDashboardCubit, HostDashboardState>(
          builder: (context, state) {
            // Get wallet balance from state or use default
            double walletBalance = 2450.75; // Default value
            if (state is HostDashboardLoaded) {
              walletBalance = state.dashboardData.financialData.walletBalance;
            }

            return StatefulBuilder(
              builder: (context, setState) {
                return AlertDialog(
                  title: const Text('سحب الأموال'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الرصيد المتاح',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '\$${walletBalance.toStringAsFixed(2)}',
                        style: AppTextStyles.font20Bold.copyWith(
                          color: Colors.amber,
                        ),
                      ),
                      const SizedBox(height: 20),
                      TextField(
                        controller: amountController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'المبلغ',
                          border: OutlineInputBorder(),
                          prefixText: '\$ ',
                        ),
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<String>(
                        value: selectedMethod,
                        decoration: const InputDecoration(
                          labelText: 'طريقة السحب',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'bankTransfer',
                            child: Text('تحويل بنكي'),
                          ),
                          DropdownMenuItem(
                            value: 'paypal',
                            child: Text('PayPal'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            selectedMethod = value!;
                          });
                        },
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(s.cancel),
                    ),
                    EnhancedButton(
                      text: s.withdraw,
                      onPressed: () {
                        // TODO: Implement withdrawal logic
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم إرسال طلب السحب بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      },
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }

  // Dummy data methods (to be removed when API is fully integrated)
  List<ReservationSummary> _getDummyBookings() {
    return [
      const ReservationSummary(
        id: 1,
        guestName: 'أحمد محمد',
        propertyName: 'شقة فاخرة في الرياض',
        checkIn: '2024-01-15',
        checkOut: '2024-01-18',
        nights: 3,
        amount: 450.0,
        status: 'confirmed',
      ),
      const ReservationSummary(
        id: 2,
        guestName: 'سارة أحمد',
        propertyName: 'فيلا مع مسبح',
        checkIn: '2024-01-20',
        checkOut: '2024-01-25',
        nights: 5,
        amount: 750.0,
        status: 'processing',
      ),
    ];
  }

  List<ReviewSummary> _getDummyReviews() {
    return [
      const ReviewSummary(
        id: 1,
        guestName: 'فاطمة الزهراء',
        propertyName: 'شقة فاخرة في الرياض',
        rating: 5.0,
        comment: 'مكان رائع ونظيف، الخدمة ممتازة والموقع مثالي',
        date: '2024-01-10',
      ),
      const ReviewSummary(
        id: 2,
        guestName: 'عبدالله السعد',
        propertyName: 'فيلا مع مسبح',
        rating: 4.5,
        comment: 'إقامة جميلة، المسبح رائع والفيلا واسعة',
        date: '2024-01-08',
      ),
    ];
  }
}

// Host Stat Card Widget
class HostStatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const HostStatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Container(
        padding: const EdgeInsets.all(5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.05),
              color.withValues(alpha: 0.02),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: color.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.trending_up_rounded,
                    color: color,
                    size: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: AppTextStyles.font24Bold.copyWith(
                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Host Booking Card Widget
class HostBookingCard extends StatelessWidget {
  final ReservationSummary booking;
  final S s;

  const HostBookingCard({
    super.key,
    required this.booking,
    required this.s,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        booking.guestName,
                        style: AppTextStyles.font18Bold.copyWith(
                          color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        booking.propertyName,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(booking.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _getStatusColor(booking.status).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    _getStatusText(booking.status),
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: _getStatusColor(booking.status),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_today_rounded,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 6),
                            Text(
                              s.dates,
                              style: AppTextStyles.font12Regular.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${booking.checkIn} - ${booking.checkOut}',
                          style: AppTextStyles.font14SemiBold.copyWith(
                            color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.nights_stay_rounded,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '${booking.nights} ${s.nights}',
                            style: AppTextStyles.font12Regular.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '\$${booking.amount.toStringAsFixed(2)}',
                        style: AppTextStyles.font18Bold.copyWith(
                          color: AppColors.yellow,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'confirmed':
        return Colors.green;
      case 'processing':
        return Colors.orange;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'confirmed':
        return 'مؤكدة';
      case 'processing':
        return 'قيد المعالجة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }
}

// Host Review Card Widget
class HostReviewCard extends StatelessWidget {
  final ReviewSummary review;
  final S s;

  const HostReviewCard({
    super.key,
    required this.review,
    required this.s,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        review.guestName,
                        style: AppTextStyles.font18Bold.copyWith(
                          color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        review.propertyName,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.amber.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.star_rounded,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        review.rating.toString(),
                        style: AppTextStyles.font14Bold.copyWith(
                          color: Colors.amber[800],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.format_quote_rounded,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 6),
                      Text(
                        s.guestComment,
                        style: AppTextStyles.font12Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    review.comment,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                      height: 1.4,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.access_time_rounded,
                  size: 14,
                  color: Colors.grey[500],
                ),
                const SizedBox(width: 4),
                Text(
                  review.date,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
