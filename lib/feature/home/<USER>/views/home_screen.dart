import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/services/location_service.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/home/<USER>/cubit/home_cubit.dart';
import 'package:gather_point/core/widgets/shimmer_components.dart';
import 'package:gather_point/feature/home/<USER>/widgets/home_search_results.dart';
import 'package:gather_point/feature/settings/presentation/views/settings_view.dart';
import 'package:gather_point/feature/reels/presentation/views/single_reel_page.dart';
import 'package:gather_point/feature/home/<USER>/views/explore_list_view.dart';
import 'package:hive/hive.dart';

class GatherPointHome extends StatelessWidget {
  const GatherPointHome({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HomeCubit(
        dioConsumer: DioConsumer(
          dio: getIt<Dio>(),
          profileBox: getIt<Box<UserEntity>>(),
        ),
        locationService: getIt<LocationService>(),
      )..initializeHome(),
      child: const _GatherPointHomeContent(),
    );
  }
}

class _GatherPointHomeContent extends StatefulWidget {
  const _GatherPointHomeContent();

  @override
  State<_GatherPointHomeContent> createState() =>
      _GatherPointHomeContentState();
}

class _GatherPointHomeContentState extends State<_GatherPointHomeContent> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _showCityDialog(List<City> cities, City? currentCity) {
    final s = S.of(context);
    // Capture the HomeCubit reference before showing dialog
    final homeCubit = context.read<HomeCubit>();

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          backgroundColor: dialogContext.cardColor,
          title: Text(s.selectCity,
              style: AppTextStyles.font18Bold
                  .copyWith(color: dialogContext.primaryTextColor)),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: cities.length,
              itemBuilder: (context, index) {
                final city = cities[index];
                return ListTile(
                  title: Text(city.name,
                      style: AppTextStyles.font16Regular
                          .copyWith(color: dialogContext.primaryTextColor)),
                  hoverColor: dialogContext.accentColor.withValues(alpha: 0.1),
                  onTap: () {
                    debugPrint(
                        'User selected city: ${city.name} (ID: ${city.id})');
                    // Use the captured cubit reference instead of context.read
                    homeCubit.selectCity(city);
                    Navigator.pop(dialogContext);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _performSearch(String query) {
    try {
      context.read<HomeCubit>().performSearch(query);
    } catch (e) {
      debugPrint('HomeCubit not available for search: $e');
    }
  }

  void _clearSearch() {
    _searchController.clear();
    FocusScope.of(context).unfocus();
    try {
      context.read<HomeCubit>().clearSearch();
    } catch (e) {
      debugPrint('HomeCubit not available for clear search: $e');
    }
  }

  /// Enhanced refresh functionality with animations and haptic feedback
  Future<void> _onRefresh() async {
    try {
      // Add haptic feedback
      HapticFeedback.mediumImpact();

      // Trigger refresh in cubit
      await context.read<HomeCubit>().initializeHome();

      // Add success haptic feedback
      HapticFeedback.lightImpact();
    } catch (e) {
      debugPrint('Refresh error: $e');
      // Add error haptic feedback
      HapticFeedback.heavyImpact();

      // Show error snackbar with animation
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحديث البيانات',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.white,
              ),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: _onRefresh,
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return BlocConsumer<HomeCubit, HomeState>(
      listener: (context, state) {
        if (state is HomeLoaded && state.searchResults.isNotEmpty) {
          HomeSearchResults.show(
              context, state.searchResults, _searchController.text);
        }
      },
      builder: (context, state) {
        return BlocListener<LocaleCubit, LocaleState>(
          listener: (context, localeState) {
            // Only call updateLocalization if HomeCubit is available
            try {
              context.read<HomeCubit>().updateLocalization();
            } catch (e) {
              // HomeCubit not available yet, ignore
              debugPrint('HomeCubit not available for locale update: $e');
            }
          },
          child: Builder(
            builder: (context) {
              if (state is HomeLoading) {
                return _buildLoadingScreen(context);
              }

              if (state is HomeError) {
                return _buildErrorScreen(context, state.message, s);
              }

              final homeState = state as HomeLoaded;

              // Debug: Print current state
              debugPrint(
                  'Home State - City: ${homeState.currentCity?.name}, Categories: ${homeState.categories.length}, Reels: ${homeState.reels.length}');

              return Scaffold(
                backgroundColor: AppColors.yellow, // Yellow background like in design
                body: SafeArea(
                  child: RefreshIndicator(
                    onRefresh: _onRefresh,
                    color: Colors.black,
                    backgroundColor: AppColors.yellow,
                    strokeWidth: 3,
                    displacement: 60,
                    child: ListView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: [
                        // Header Section
                        _buildHeader(context, homeState, s),

                        const SizedBox(height: 16),

                        // Search Bar
                        _buildSearchBar(context, s),

                        const SizedBox(height: 24),

                        // Categories Section
                        _buildCategoriesSection(context, homeState, s),

                        const SizedBox(height: 32),

                        // Featured Section
                        _buildFeaturedSection(context, homeState, s),

                        const SizedBox(height: 50), // Bottom padding for navigation
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, HomeLoaded homeState, S s) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // App Title and Location
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // App Title with Location Icon
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.location_on,
                        color: AppColors.yellow,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Gather Point',
                      style: AppTextStyles.font24Bold.copyWith(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // City Selector
                GestureDetector(
                  onTap: homeState.cities.isNotEmpty
                      ? () => _showCityDialog(homeState.cities, homeState.currentCity)
                      : null,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.location_on,
                          color: AppColors.yellow,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          homeState.currentCity?.name ?? 'Jeddah',
                          style: AppTextStyles.font14Medium.copyWith(
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 4),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: Colors.white,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Settings Button
          Container(
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SettingsView(),
                  ),
                );
              },
              icon: const Icon(
                Icons.settings,
                color: AppColors.yellow,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context, S s) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          setState(() {}); // Trigger rebuild to show/hide clear button
        },
        onTap: () {
          _performSearch(_searchController.text);
        },
        onSubmitted: (value) {
          _performSearch(value);
        },
        style: AppTextStyles.font16Regular.copyWith(
          color: Colors.white,
        ),
        decoration: InputDecoration(
          hintText: 'ابحث عن وجهتك المفضلة.....',
          hintStyle: AppTextStyles.font16Regular.copyWith(
            color: Colors.white.withValues(alpha: 0.7),
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: AppColors.yellow,
            size: 24,
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: _clearSearch,
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                )
              : null,
          filled: true,
          fillColor: const Color(0xFF2B2B2B), // Dark background like in design
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: const BorderSide(
              color: AppColors.yellow,
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 20,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingScreen(BuildContext context) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Enhanced header shimmer with animation
              ShimmerComponents.homeHeader(context),

              const SizedBox(height: 20),

              // Enhanced search bar shimmer
              ShimmerComponents.searchBar(context),

              const SizedBox(height: 30),

              // Categories section shimmer
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section title shimmer
                    TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 800),
                      tween: Tween(begin: 0.0, end: 1.0),
                      builder: (context, value, child) {
                        return Transform.translate(
                          offset: Offset(-30 * (1 - value), 0),
                          child: Opacity(
                            opacity: value,
                            child: ShimmerComponents.buildShimmer(
                              context: context,
                              child: Container(
                                width: 120,
                                height: 18,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(9),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    // Categories list shimmer
                    ShimmerComponents.categoriesList(context),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Reels section shimmer
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section title shimmer
                    TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 1000),
                      tween: Tween(begin: 0.0, end: 1.0),
                      builder: (context, value, child) {
                        return Transform.translate(
                          offset: Offset(-30 * (1 - value), 0),
                          child: Opacity(
                            opacity: value,
                            child: ShimmerComponents.buildShimmer(
                              context: context,
                              child: Container(
                                width: 100,
                                height: 18,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(9),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    // Reels list shimmer
                    ShimmerComponents.reelsList(context),
                  ],
                ),
              ),

              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorScreen(BuildContext context, String message, S s) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: context.secondaryTextColor,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.font16Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                try {
                  context.read<HomeCubit>().initializeHome();
                } catch (e) {
                  debugPrint('HomeCubit not available for retry: $e');
                  // Optionally navigate back or show a different error
                }
              },
              child: Text(s.retry),
            ),
          ],
        ),
      ),
    );
  }
}
