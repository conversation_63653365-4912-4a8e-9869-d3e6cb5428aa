import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/generated/l10n.dart';

class PlaceHostSection extends StatelessWidget {
  final HosterModel hoster;

  const PlaceHostSection({
    super.key,
    required this.hoster,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final registeredDate = DateTime.tryParse(hoster.registeredSince);
    final yearsHosting = registeredDate != null
        ? DateTime.now().difference(registeredDate).inDays ~/ 365
        : 0;

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 35,
                    backgroundColor: context.accentColor.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.person_rounded,
                      size: 35,
                      color: context.accentColor,
                    ),
                  ),
                  // Verification badge (show if rating is high)
                  if (hoster.rating != null && hoster.rating! >= 4.5)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 2,
                          ),
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            '${s.hostedBy} ${hoster.name}',
                            style: AppTextStyles.font18Bold.copyWith(
                              color: context.primaryTextColor,
                            ),
                          ),
                        ),
                        if (hoster.rating != null && hoster.rating! > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.yellow.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.star_rounded,
                                  size: 14,
                                  color: AppColors.yellow,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  hoster.rating!.toStringAsFixed(1),
                                  style: AppTextStyles.font12SemiBold.copyWith(
                                    color: AppColors.yellow,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      yearsHosting > 0
                          ? '$yearsHosting ${s.yearsHosting}'
                          : 'مستضيف جديد', // TODO: Add to localization
                      style: AppTextStyles.font14Regular.copyWith(
                        color: context.secondaryTextColor,
                      ),
                    ),
                    if (hoster.totalNoOfRates != null && hoster.totalNoOfRates! > 0) ...[
                      const SizedBox(height: 4),
                      Text(
                        '${hoster.totalNoOfRates} ${s.reviews}',
                        style: AppTextStyles.font12Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Contact buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: Implement contact host functionality
                  },
                  icon: const Icon(Icons.message_rounded, size: 18),
                  label: Text(s.contactHost),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: context.accentColor,
                    side: BorderSide(color: context.accentColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: Implement call host functionality
                  },
                  icon: const Icon(Icons.phone_rounded, size: 18),
                  label: Text(s.phone),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: context.accentColor,
                    side: BorderSide(color: context.accentColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
