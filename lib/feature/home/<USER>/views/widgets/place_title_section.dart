import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/generated/l10n.dart';

class PlaceTitleSection extends StatelessWidget {
  final PlaceDetailModel placeDetail;

  const PlaceTitleSection({
    super.key,
    required this.placeDetail,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            placeDetail.title,
            style: AppTextStyles.font24Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.location_on_rounded,
                size: 18,
                color: context.accentColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "${placeDetail.city} - ${placeDetail.country}",
                  style: AppTextStyles.font16Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              if (placeDetail.rating != null) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.yellow.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.star_rounded, size: 16, color: AppColors.yellow),
                      const SizedBox(width: 4),
                      Text(
                        placeDetail.rating?.toStringAsFixed(1) ?? '0',
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: AppColors.yellow,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '(${placeDetail.noOfRates ?? 0} ${s.reviews})',
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.secondaryTextColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ],
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: context.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${placeDetail.price.toStringAsFixed(0)} ${s.perNight}',
                  style: AppTextStyles.font16Bold.copyWith(
                    color: context.accentColor,
                  ),
                ),
              ),
            ],
          ),
          // Tourism Permit Number - TODO: Enable when IDE recognizes the field
          // if (placeDetail.tourismPermitNumber != null &&
          //     placeDetail.tourismPermitNumber!.isNotEmpty) ...[
          //   const SizedBox(height: 16),
          //   Container(
          //     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          //     decoration: BoxDecoration(
          //       color: Colors.green.withValues(alpha: 0.1),
          //       borderRadius: BorderRadius.circular(8),
          //       border: Border.all(
          //         color: Colors.green.withValues(alpha: 0.3),
          //         width: 1,
          //       ),
          //     ),
          //     child: Row(
          //       children: [
          //         Icon(
          //           Icons.verified_rounded,
          //           size: 18,
          //           color: Colors.green[700],
          //         ),
          //         const SizedBox(width: 8),
          //         Expanded(
          //           child: Text(
          //             '${s.tourismPermitNumber}: ${placeDetail.tourismPermitNumber}',
          //             style: AppTextStyles.font14Regular.copyWith(
          //               color: Colors.green[700],
          //             ),
          //           ),
          //         ),
          //       ],
          //     ),
          //   ),
          // ],
        ],
      ),
    );
  }
}
