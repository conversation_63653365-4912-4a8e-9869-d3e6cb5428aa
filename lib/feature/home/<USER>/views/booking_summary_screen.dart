import 'package:flutter/material.dart';
import 'dart:math';

import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/feature/profile/presentation/views/my_bookings_screen.dart';

class BookingSummaryScreen extends StatelessWidget {
  final String placeTitle;
  final DateTime checkIn;
  final DateTime checkOut;
  final int adults;
  final int children;
  final String paymentMethod;
  final double totalPrice;

  const BookingSummaryScreen({
    super.key,
    required this.placeTitle,
    required this.checkIn,
    required this.checkOut,
    required this.adults,
    required this.children,
    required this.paymentMethod,
    required this.totalPrice,
  });

  @override
  Widget build(BuildContext context) {
    String bookingId = generateBookingId();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Summary'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFF8F9FA), Color(0xFFE0E0E0)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 80),
            const SizedBox(height: 20),
            const Text(
              'Reservation Confirmed!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 30),

            // Card for Booking ID
            Card(
              elevation: 5,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 30),
                child: Column(
                  children: [
                    const Text(
                      'Booking ID',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      bookingId,
                      style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold, letterSpacing: 2),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 30),

            // Card for full booking details
            Card(
              elevation: 5,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    detailRow(Icons.location_on, 'Place', placeTitle),
                    const SizedBox(height: 10),
                    detailRow(Icons.calendar_month, 'Check-in', checkIn.toLocal().toString().split(' ')[0]),
                    detailRow(Icons.calendar_today, 'Check-out', checkOut.toLocal().toString().split(' ')[0]),
                    const SizedBox(height: 10),
                    detailRow(Icons.people, 'Guests', 'Adults: $adults, Children: $children'),
                    const SizedBox(height: 10),
                    detailRow(Icons.payment, 'Payment Method', paymentMethod),
                    const SizedBox(height: 20),
                    Center(
                      child: Text(
                        'Total Paid: €${totalPrice.toStringAsFixed(2)}',
                        style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.green),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const Spacer(),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.popUntil(context, (route) => route.isFirst);
                    },
                    icon: const Icon(Icons.home),
                    label: const Text('Home'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blueGrey,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 50),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MyBookingsScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.list_alt),
                    label: const Text('My Bookings'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.yellow,
                      foregroundColor: Colors.black,
                      minimumSize: const Size(double.infinity, 50),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget detailRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 24, color: Colors.blueAccent),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            '$label: $value',
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ],
    );
  }

  String generateBookingId() {
    final random = Random();
    final id = random.nextInt(900000) + 100000;
    return id.toString();
  }
}
