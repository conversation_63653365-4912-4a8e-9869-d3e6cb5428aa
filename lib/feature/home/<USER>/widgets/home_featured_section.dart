import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/feature/reels/presentation/views/single_reel_page.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/utils/sound_manager.dart';

class HomeFeaturedSection extends StatefulWidget {
  final List<Map<String, dynamic>> reels;
  final bool isLoading;

  const HomeFeaturedSection({
    super.key,
    required this.reels,
    required this.isLoading,
  });

  @override
  State<HomeFeaturedSection> createState() => _HomeFeaturedSectionState();
}

class _HomeFeaturedSectionState extends State<HomeFeaturedSection>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start subtle animation
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    if (widget.isLoading) {
      return _buildLoadingState(context);
    }

    if (widget.reels.isEmpty) {
      return const SizedBox.shrink();
    }

    // Get the first reel as featured
    final featuredReel = widget.reels.first;
    final image = (featuredReel['gallery'] as List?)?.isNotEmpty == true
        ? featuredReel['gallery'][0]['image']
        : featuredReel['image'] ?? 'https://via.placeholder.com/400x200';
    final title = featuredReel['title'] ?? '';
    final price = featuredReel['price'] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Featured Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            s.featured,
            style: AppTextStyles.font20Bold.copyWith(
              color: context.accentColor,
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Featured Reel Card
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () async {
                      // Play click sound
                      try {
                        await SoundManager.playClickSound();
                      } catch (e) {
                        debugPrint('Click sound error: $e');
                      }

                      if (context.mounted) {
                        final reelId = featuredReel['id'];
                        if (reelId != null) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SingleReelPage(
                                selectedReelId: reelId,
                                showBackButton: true,
                              ),
                            ),
                          );
                        }
                      }
                    },
                    child: Container(
                      height: 200,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: context.cardShadow,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Stack(
                          children: [
                            // Background Image
                            Image.network(
                              image,
                              width: double.infinity,
                              height: 200,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => Container(
                                width: double.infinity,
                                height: 200,
                                color: context.secondaryTextColor.withValues(alpha: 0.1),
                                child: Icon(
                                  Icons.image_not_supported_rounded,
                                  color: context.secondaryTextColor,
                                  size: 48,
                                ),
                              ),
                            ),

                            // Gradient Overlay
                            Container(
                              width: double.infinity,
                              height: 200,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withValues(alpha: 0.7),
                                  ],
                                ),
                              ),
                            ),

                            // Play Button
                            Positioned(
                              left: 16,
                              top: 16,
                              child: Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.6),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.play_arrow_rounded,
                                  color: Colors.white,
                                  size: 28,
                                ),
                              ),
                            ),

                            // Content Overlay
                            Positioned(
                              bottom: 16,
                              left: 16,
                              right: 16,
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        if (title.isNotEmpty)
                                          Text(
                                            title,
                                            style: AppTextStyles.font16Bold.copyWith(
                                              color: Colors.white,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: context.accentColor,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Text(
                                      'SAR $price',
                                      style: AppTextStyles.font12Bold.copyWith(
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Container(
            width: 100,
            height: 24,
            decoration: BoxDecoration(
              color: context.secondaryTextColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        const SizedBox(height: 12),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Container(
            height: 200,
            decoration: BoxDecoration(
              color: context.secondaryTextColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
          ),
        ),
      ],
    );
  }
}
